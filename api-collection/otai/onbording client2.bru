meta {
  name: onbording client2
  type: http
  seq: 9
}

post {
  url: https://ot.innovation.dev.oneteam.services/api/external/clients
  body: json
  auth: bearer
}

auth:bearer {
  token: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
}

body:json {
  {
      "client": {
          "id": "$number({{ClientProject.a3LIwQjNDl}})",
          "name": {{ClientProject.aVMSApXyr6}},
          "users": "[$map($split({{ClientProject.aA36UshxKn}}, "|"), function($p, $i) {
                  {
                      "firstName": $p,
                      "lastName": [$split({{ClientProject.aN6Fjxgw2c}}, "|")][$i],
                      "email": [$split({{ClientProject.aE8o7GsvLE}}, "|")][$i],
                      "role": [$split({{ClientProject.aG3ztXxzug}}, "|")][$i]
                  }
              })]"
          
      },
      "triggeredByUser": 
          {
          "email": {{form.aHcEAlXdEF.answer}},
          "firstName": {{form.aw7NGSt077.answer}},
          "lastName": {{form.apuNzNrfo5.answer}},
          "role": {{form.aJl2Owk7rC.answer}}
          }
  }
}
