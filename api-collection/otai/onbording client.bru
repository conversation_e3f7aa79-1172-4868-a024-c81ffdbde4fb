meta {
  name: onbording client
  type: http
  seq: 8
}

post {
  url: https://ot.innovation.dev.oneteam.services/api/external/clients
  body: json
  auth: bearer
}

auth:bearer {
  token: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
}

body:json {
  {
      "client": {
          "id": 401,
          "name": "Ex min",
          "users": [
              {
                  "firstName": "Hanrui",
                  "lastName": "Test",
                  "email": "<EMAIL>"
              },
              {
                  "firstName": "Ada",
                  "lastName": "Lovelace",
                  "email": "<EMAIL>"
              }
          ]
  //       ,
  //         "project": {
  //             "id": "125125125", // change as needed
  //             "name": "min ex ET", // change as needed
  //             "startDate": "2024-01-01", // change as needed
  //             "endDate": "2026-12-01", // change as needed
  //             "year": "2024",
  //             "engagementType": "Engagement Test", // change as needed
  //             "engagementManager": {
  //                 "firstName": "Ada",
  //                 "lastName": "Lovelace",
  //                 "email": "<EMAIL>"
  //             },
  //             "projectManager": {
  //                 "firstName": "Ada",
  //                 "lastName": "Lovelace",
  //                 "email": "<EMAIL>"
  //             },
  //             "entities": [
  //                 {
  //                     "id": "1",
  //                     "name": "a"
  //                 },
  //                 {
  //                     "id": "2",
  //                     "name": "b"
  //                 },
  //                 {
  //                     "id": "3",
  //                     "name": "c"
  //                 }
  //             ],
  //             "users": [
  //                 {
  //                 "firstName": "John",
  //                 "lastName": "Hopfield",
  //                 "email": "<EMAIL>",
  //                     "type": "client"
  //                 },
  //                 {
  //                     "firstName": "Ada",
  //                     "lastName": "Lovelace",
  //                     "email": "<EMAIL>",
  //                     "type": "host"
  //                 } // .....
  //             ]
  //         }
      },
      "triggeredByUser": {
          "firstName": "Ada",
          "lastName": "Lovelace",
          "email": "<EMAIL>"
      }
  }
}
