package services.oneteam.ai.app.domains.user

import io.ktor.http.*
import io.ktor.resources.*
import io.ktor.server.resources.*
import io.ktor.server.response.*
import io.ktor.server.routing.Route
import services.oneteam.ai.app.extensions.buildPageRequest
import services.oneteam.ai.shared.domains.workspace.*
import services.oneteam.ai.shared.domains.user.UserSearchCriteria
import services.oneteam.ai.shared.domains.user.UserService
import services.oneteam.ai.shared.middlewares.RequestContext
import kotlin.coroutines.coroutineContext

@Suppress("unused")
@Resource("/users")
private class UserRoute() {
    @Resource("/search")
    class Search(val users: UserRoute)
}

fun Route.userEndpoints(
    userService: UserService
) {
    get<UserRoute.Search> {
        val tenantId = coroutineContext[RequestContext]!!.tenant.id
        val pageRequest = call.request.buildPageRequest(WorkspaceRepository.SORTABLE_FIELDS)
        val searchCriteria = UserSearchCriteria.fromRequest(tenantId, call.request.queryParameters)

        val w = userService.search(pageRequest, searchCriteria)
        call.respond(w)
    }
}

fun UserSearchCriteria.Companion.fromRequest(
    tenantId: Long,
    parameters: Parameters
): UserSearchCriteria {
    val searchTerm = parameters["search"] ?: ""

    return UserSearchCriteria(
        tenantId = tenantId,
        searchTerm = searchTerm,
    )

}
