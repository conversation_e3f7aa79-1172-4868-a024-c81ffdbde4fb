package services.oneteam.ai.app.domains.user

import io.ktor.http.*
import io.ktor.resources.*
import io.ktor.server.plugins.*
import io.ktor.server.request.*
import io.ktor.server.resources.*
import io.ktor.server.resources.post
import io.ktor.server.resources.put
import io.ktor.server.response.*
import io.ktor.server.routing.Route
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.jsonObject
import services.oneteam.ai.app.domains.workspace.Workspaces
import services.oneteam.ai.app.extensions.buildPageRequest
import services.oneteam.ai.app.middlewares.addEventToCallAttributes
import services.oneteam.ai.shared.domains.TypeToJsonElementConverter
import services.oneteam.ai.shared.domains.collection.form.*
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.collection.foundation.getFoundationConfiguration
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.proxy.ProxyService
import services.oneteam.ai.shared.domains.proxy.includeInternalServiceAccount
import services.oneteam.ai.shared.domains.workspace.*
import kotlin.text.toLong
import services.oneteam.ai.shared.domains.user.User
import services.oneteam.ai.shared.domains.user.UserSearchCriteria
import services.oneteam.ai.shared.domains.user.UserService
import services.oneteam.ai.shared.middlewares.RequestContext
import kotlin.coroutines.coroutineContext

@Suppress("unused")
@Resource("/users")
private class UserRoute() {
    @Resource("/search")
    class Search(val users: UserRoute)
}

fun Route.userEndpoints(
    userService: UserService
) {
    post<UserRoute> {
        val userForCreate = call.receive<User.ForCreate>()
        val createdUser = userService.create(userForCreate)
        call.respond(createdUser)
    }

    get<UserRoute.Search> {
        val tenantId = coroutineContext[RequestContext]!!.tenant.id
        val pageRequest = call.request.buildPageRequest(WorkspaceRepository.SORTABLE_FIELDS)
        val searchCriteria = UserSearchCriteria.fromRequest(tenantId, call.request.queryParameters)

        val w = userService.search(pageRequest, searchCriteria)
        call.respond(w)
    }
}

fun UserSearchCriteria.Companion.fromRequest(
    tenantId: Long,
    parameters: Parameters
): UserSearchCriteria {
    val searchTerm = parameters["search"] ?: ""

    return UserSearchCriteria(
        tenantId = tenantId,
        searchTerm = searchTerm,
    )

}
