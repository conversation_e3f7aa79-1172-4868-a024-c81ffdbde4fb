package services.oneteam.ai.app

import automergeRepo.src.main.kotlin.Repo
import automergeRepo.src.main.kotlin.RepoOptions
import io.ktor.serialization.kotlinx.*
import io.ktor.serialization.kotlinx.json.*
import io.ktor.server.application.*
import io.ktor.server.auth.*
import io.ktor.server.auth.jwt.*
import io.ktor.server.config.*
import io.ktor.server.netty.*
import io.ktor.server.plugins.cors.routing.*
import io.ktor.server.plugins.requestvalidation.*
import io.ktor.server.plugins.statuspages.*
import io.ktor.server.resources.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.server.sessions.*
import io.ktor.server.websocket.*
import io.ktor.util.*
import io.opentelemetry.api.GlobalOpenTelemetry
import io.opentelemetry.instrumentation.ktor.v3_0.KtorServerTelemetry
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.decodeFromStream
import network.WebSocketClientAdapter
import org.jetbrains.exposed.sql.transactions.transaction
import org.koin.ktor.ext.inject
import org.koin.ktor.plugin.Koin
import org.slf4j.LoggerFactory
import services.oneteam.ai.app.domains.actions.internalActionEndpoints
import services.oneteam.ai.app.domains.auth.AuthService
import services.oneteam.ai.app.domains.auth.authenticatedEndpoints
import services.oneteam.ai.app.domains.auth.unauthenticatedEndpoints
import services.oneteam.ai.app.domains.collection.*
import services.oneteam.ai.app.domains.document.documentEndpoints
import services.oneteam.ai.app.domains.document.documentEventsEndpoints
import services.oneteam.ai.app.domains.event.eventEndpoints
import services.oneteam.ai.app.domains.flow.*
import services.oneteam.ai.app.domains.flowStepTypeConfiguration.flowStepTypeConfigurationEndpoints
import services.oneteam.ai.app.domains.user.userEndpoints
import services.oneteam.ai.app.domains.webhook.externalWebhookEndpoints
import services.oneteam.ai.app.domains.websocket.webSocketEndpoints
import services.oneteam.ai.app.domains.workspace.sampleDataEndpoints
import services.oneteam.ai.app.domains.workspace.workspaceEndpoints
import services.oneteam.ai.app.domains.workspace.workspaceVersionEndpoints
import services.oneteam.ai.app.internal.InternalProxyService
import services.oneteam.ai.app.middlewares.*
import services.oneteam.ai.flow.event.TriggerFlowEventListener
import services.oneteam.ai.flow.execution.CustomDispatcher
import services.oneteam.ai.flow.execution.FlowExecutionRepository
import services.oneteam.ai.flow.execution.FlowExecutionService
import services.oneteam.ai.flow.pubsub.PubSubService
import services.oneteam.ai.shared.Checks
import services.oneteam.ai.shared.DataSeeder
import services.oneteam.ai.shared.ExposedInitializer
import services.oneteam.ai.shared.FlywayMigration
import services.oneteam.ai.shared.database.DatabaseLive
import services.oneteam.ai.shared.domains.actions.FilePressService
import services.oneteam.ai.shared.domains.auth.UserSession
import services.oneteam.ai.shared.domains.collection.form.BlobService
import services.oneteam.ai.shared.domains.collection.form.FormRepository
import services.oneteam.ai.shared.domains.collection.form.FormService
import services.oneteam.ai.shared.domains.collection.foundation.FoundationRepository
import services.oneteam.ai.shared.domains.collection.foundation.FoundationService
import services.oneteam.ai.shared.domains.event.EventDispatcher
import services.oneteam.ai.shared.domains.event.EventService
import services.oneteam.ai.shared.domains.event.InMemoryEventQueue
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepType
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationEntity
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationRepository
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationService
import services.oneteam.ai.shared.domains.proxy.ExternalProxyService
import services.oneteam.ai.shared.domains.tenant.TenantRepository
import services.oneteam.ai.shared.domains.tenant.TenantService
import services.oneteam.ai.shared.domains.user.UserRepository
import services.oneteam.ai.shared.domains.user.UserService
import services.oneteam.ai.shared.domains.workspace.*
import services.oneteam.ai.shared.domains.workspace.document.ApiDocumentService
import services.oneteam.ai.shared.domains.workspace.document.RepoDocumentService
import java.util.*
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds

fun main(args: Array<String>) {
    io.ktor.server.netty.EngineMain.main(args)
}

@OptIn(ExperimentalSerializationApi::class)
fun Application.module() {
    val logger = LoggerFactory.getLogger(Application::class.java)
    install(Koin) {
        ApplicationConfig("application.yaml")
        modules(appModule)
    }
    setupConfig()
    val appConfig by inject<AppConfig>()

    val flowDispatcher = CustomDispatcher(
        "flow-runner",
        appConfig.flows.numberOfThreads,
        appConfig.flows.maxConcurrentCoroutines
    )
    this.monitor.subscribe(ApplicationStopped) {
        flowDispatcher.shutdown()
        logger.info("Application stopped for ${appConfig.websiteName}")
    }

    install(Resources)
    install(io.ktor.server.plugins.contentnegotiation.ContentNegotiation) {
        json(Json {
            encodeDefaults = true
        })
    }

    if (appConfig.telemetry.enabled) {
        install(KtorServerTelemetry) {
            setOpenTelemetry(GlobalOpenTelemetry.get())
        }
    }

    val constraintMapping =
        Json.decodeFromStream<Array<ConstraintMapping>>(this::class.java.getResourceAsStream("/database_constraint_to_localization_mapping.json")!!)

    install(StatusPages) {
        configureStatusPages(constraintMapping)
    }

    val database = DatabaseLive(appConfig.databaseConfig)
    val dictionary = ResourceBundle.getBundle("dictionary")

    if (appConfig.flyway.enabled) {
        // run migrations
        runBlocking {
            FlywayMigration().migrate(appConfig.flyway, database.privileged.dataSource)
        }
    }

    val checks = Checks()
    val proxyService = ExternalProxyService(appConfig.otaiServiceAccount.token)
    val internalProxyService = InternalProxyService(this, appConfig.otaiServiceAccount.token)
    val tenantRepository = TenantRepository(database)
    val tenantService = TenantService(tenantRepository)
    val userRepository = UserRepository(checks)
    val authService = AuthService(userRepository)
    val foundationRepository = FoundationRepository()

    val formRepository = FormRepository(checks)
    val workspaceRepository = WorkspaceRepository(checks)
    val flowExecutionRepository = FlowExecutionRepository()
    val uploadService =
        BlobService(appConfig.storage.accountName, appConfig.storage.accessKey, appConfig.storage.uploadContainerName)
    val flowStepTypeConfigurationRepository = FlowStepTypeConfigurationRepository()
    val flowStepTypeConfigurationService = FlowStepTypeConfigurationService(flowStepTypeConfigurationRepository)

    val apiDocumentService = ApiDocumentService(
        proxyService, flowStepTypeConfigurationService
    )

    val repoOptions = RepoOptions(
        networkAdapters = listOf(
            WebSocketClientAdapter(
                appConfig.syncServerUrl, appConfig.otaiServiceAccount.token
            )
        )
    )

    val repo = if (appConfig.documentStrategy == "REPO") {
        Repo(repoOptions)
    } else {
        null
    }

    val documentService = when (appConfig.documentStrategy) {
        "API" -> apiDocumentService

        "REPO" -> RepoDocumentService(
            apiDocumentService, repo!!
        )

        else -> apiDocumentService
    }

    install(EventMiddleware(proxyService))

    val workspaceVersionRepository = WorkspaceVersionRepository()

    val workspaceVersionService =
        WorkspaceVersionService(workspaceRepository, documentService, workspaceVersionRepository, checks)
    val userService = UserService(userRepository)
    val foundationService = FoundationService(foundationRepository, workspaceVersionService, checks)
    val formService = FormService(
        formRepository,
        workspaceRepository,
        documentService,
        workspaceVersionRepository,
        uploadService,
        foundationService,
        workspaceVersionService,
        checks
    )
    val foundationConfigurationService =
        FoundationConfigurationService(
            checks,
            dictionary,
            foundationRepository,
        )
    val workspaceService =
        WorkspaceService(
            workspaceRepository,
            foundationService,
            documentService,
            foundationConfigurationService,
            workspaceVersionService,
            formService,
            checks,
        )

    // Event Queue
    val eventDispatcher = EventDispatcher(InMemoryEventQueue(), Dispatchers.Default)
    val eventListener = TriggerFlowEventListener(
        workspaceVersionService,
        proxyService,
        flowStepTypeConfigurationService,
        appConfig.flows.runExecutionImmediately,
        timeoutMins = appConfig.flows.timeoutMins
    )
    eventDispatcher.register(eventListener)
    val eventService = EventService(eventDispatcher)

    // flows
    val flowExecutionService = FlowExecutionService(
        flowExecutionRepository,
        documentService,
        proxyService,
        internalProxyService,
        workspaceVersionService,
        formRepository,
        foundationService,
        flowStepTypeConfigurationService,
        flowDispatcher,
        appConfig.toggles.useExecutionStepFactoryV1,
        appConfig.documentStrategy,
        appConfig.flows.includeLogging,
        appConfig.flows.skipStepUpdates,
        appConfig.flows.skipVariableUpdates,
        repo,
    )

    val filePressService = FilePressService(
        appConfig.filePress.url, proxyService, uploadService
    )

    PubSubService.initialize(
        appConfig.azureWebPubSub.connectionString,
        appConfig.azureWebPubSub.hubName,
        appConfig.azureWebPubSub.tokenExpirySeconds
    )

    eventDispatcher.register(eventListener)

    // init
    ExposedInitializer().init(appConfig.databaseConfig.audit, appConfig.databaseConfig.privileged.rls, database)

    // seed
    if (appConfig.seedData) {
        //you can chain these seed calls to seed multiple things
        transaction(database.connectSuperUser()) {
            DataSeeder(database).seed<FlowStepType, FlowStepTypeConfigurationEntity>("/StepTypeSeed.json", true)
        }
    }

    install(CORS) {
        runBlocking {
            configureByHosts(
                tenantRepository.getAll().flatMap { listOf(it.originUrl, it.internalUrl) })
        }
    }
    install(DevToolsPlugin) {
        applicationConfig = appConfig
    }
    install(CurrentTenantPlugin) {
        tenantServiceConfig = tenantService
        applicationConfig = appConfig
    }
    install(IgnoreTrailingSlash)
    install(RequestValidation) {
        configureRequestValidation()
    }
    install(Sessions) {
        val secretEncryptKey = hex(appConfig.cookie.secretEncryptKey)
        val secretSignKey = hex(appConfig.cookie.secretSignKey)
        cookie<UserSession>(appConfig.cookie.name) {
            cookie.path = "/ai"
            cookie.secure = !appConfig.development
            cookie.maxAge = appConfig.cookie.timeoutDuration
            transform(SessionTransportTransformerEncrypt(secretEncryptKey, secretSignKey))
        }
    }

    install(Authentication) {
        bearer(AuthenticationType.BEARER.value) {
            authenticate { tokenCredential ->
                try {
                    authService.login(tokenCredential.token)
                } catch (_: Exception) {
                    null
                }
            }
        }
        session<UserSession>(AuthenticationType.SESSION.value) {
            validate { session ->
                logger.debug("session: {}", session)
                //check timeout
                if (Instant.fromEpochMilliseconds(session.maxTime) >= Clock.System.now()) {
                    session
                } else {
                    null
                }
            }
        }
        jwt(AuthenticationType.JWT.value) {
            verifier(JwtConfigurer(appConfig.jwt.publicKey).verifier)
            validate { credential ->
                JWTPrincipal(credential.payload)
            }
        }
    }

    install(WebSockets) {
        contentConverter = KotlinxWebsocketSerializationConverter(Json)
        pingPeriod = 1.minutes
        timeout = 15.seconds
        maxFrameSize = Long.MAX_VALUE
        masking = false
    }

    routing {
        get(Regex("((ai(/(api)?)?)?)?")) {
            call.respondRedirect(url = "/ai/api/", permanent = true)
        }
        route("/ai/api") {
            defaultEndpoints()
            withTenant {
                withRequestContext {
                    authenticate(AuthenticationType.JWT.value) {
                        systemEndpoints(documentService)
                    }
                    authenticate(AuthenticationType.BEARER.value) {
                        unauthenticatedEndpoints()
                    }
                    authenticate(AuthenticationType.SESSION.value, AuthenticationType.JWT.value) {
                        twoTypeAuthFormEndpoints(formService, workspaceVersionService)
                        twoTypeAuthFoundationEndpoints(foundationService, workspaceVersionService)
                        twoTypeAuthFlowExecutionEndpoints(flowExecutionService)
                        externalWebhookEndpoints(workspaceVersionService)
                    }
                    authenticate(AuthenticationType.JWT.value) {
                        eventEndpoints(eventService)
                        internalFlowExecutionEndpoints(flowExecutionService)
                        internalFlowTestEndpoints()
                        documentEventsEndpoints(formService, workspaceVersionService)
                        internalFoundationEndpoints(foundationService, workspaceVersionService)
                        internalFormEndpoints(formService, workspaceVersionService)
                        internalActionEndpoints(filePressService)
                    }
                    authenticate(AuthenticationType.SESSION.value) {
                        authenticatedEndpoints()
                        documentEndpoints(documentService)
                        workspaceEndpoints(workspaceService)
                        workspaceVersionEndpoints(workspaceVersionService)
                        foundationEndpoints(foundationService, workspaceVersionService, foundationConfigurationService)
                        formEndpoints(formService, workspaceVersionService, proxyService)
                        flowEndpoints(formService, flowExecutionService, foundationService, workspaceVersionService)
                        flowStepTypeConfigurationEndpoints(flowStepTypeConfigurationService)
                        publicFlowExecutionEndpoints(flowExecutionService)
                        webSocketEndpoints()
                        userEndpoints(userService)
                        if (appConfig.loadSampleData) {
                            sampleDataEndpoints(
                                workspaceService,
                                workspaceVersionService,
                                foundationRepository,
                                foundationService,
                                formService,
                                requestTimeoutMs = appConfig.sampleDataRequestTimeoutMs
                            )
                        }
                    }
                }

            }
        }
    }

    if (engine is NettyApplicationEngine) {
        val engine = this.engine as NettyApplicationEngine
        logger.info("Running Netty with configuration: enableHttp2 = ${engine.configuration.enableHttp2} connectionGroupSize = ${engine.configuration.connectionGroupSize} workerGroupSize = ${engine.configuration.workerGroupSize} callGroupSize = ${engine.configuration.callGroupSize}")
    }

    try {
        val jwtConfigurer = JwtConfigurer(appConfig.jwt.publicKey)
        logger.info(
            "Service token expiry: {}",
            jwtConfigurer.verifier.verify(appConfig.otaiServiceAccount.token).expiresAt
        )
    } catch (e: Exception) {
        logger.error("Error verifying JWT token: ${e.message}")
    }

    logger.info("Application started for ${appConfig.websiteName}")
}
