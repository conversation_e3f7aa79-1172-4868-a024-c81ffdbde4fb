package services.oneteam.ai.app.domains.workspace

import io.ktor.http.Parameters
import io.ktor.resources.*
import io.ktor.server.request.*
import io.ktor.server.resources.*
import io.ktor.server.resources.post
import io.ktor.server.resources.put
import io.ktor.server.response.*
import io.ktor.server.routing.Route
import kotlinx.serialization.Serializable
import services.oneteam.ai.app.extensions.buildPageRequest
import services.oneteam.ai.shared.domains.collection.form.FormRepository
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.collection.foundation.FoundationRepository
import services.oneteam.ai.shared.domains.workspace.FoundationConfiguration
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceRepository
import services.oneteam.ai.shared.domains.workspace.WorkspaceSearchCriteria
import services.oneteam.ai.shared.domains.workspace.WorkspaceService
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersion
import services.oneteam.ai.shared.domains.workspace.*

@Suppress("unused")
@Resource("/workspaces")
class Workspaces() {
    @Resource("/search")
    class Search(val workspaces: Workspaces = Workspaces())

    @Resource("/key/{key}")
    class Key(val workspaces: Workspaces = Workspaces(), val key: String)

    @Resource("/validate")
    class Validate(val workspaces: Workspaces = Workspaces())

    @Resource("{id}")
    class Id(val workspaces: Workspaces, val id: Workspace.Id) {

        @Resource("/search")
        class SearchFormsAndFoundations(val workspace: Id)

        @Resource("/configuration/foundations")
        class FoundationConfigurations(val parent: Id)

        @Resource("/configuration/forms")
        class FormConfigurations(val parent: Id)

        @Resource("/configuration/series")
        class SeriesConfigurations(val parent: Id)

        @Resource("/configuration/labels")
        class LabelConfiguration(val parent: Id)

        @Resource("/configuration/import")
        class ImportConfigurations(val parent: Id)

        @Resource("/configuration/versions")
        class Versions(val parent: Id) {
            @Resource("{versionId}")
            class Version(val parent: Versions, val versionId: WorkspaceVersion.Id)
        }

        @Resource("/foundations")
        class Foundations(val parent: Id) {
            @Resource("/key/{key}")
            class Key(val parent: Foundations, val key: Foundation.Key) {
                @Resource("/{configurationId}")
                class Config(val parent: Key, val configurationId: FoundationConfiguration.Id)
            }

            @Resource("configuration/{configurationId}")
            class ConfigurationId(val parent: Foundations, val configurationId: FoundationConfiguration.Id){
                @Resource("/canDelete")
                class CanDelete( val parent: ConfigurationId){}
            }
        }

        @Resource("/foundations/root")
        class FoundationsRoot(val parent: Id)

        @Resource("/forms")
        class Forms(val workspace: Id, val id: Workspace.Id) {
            @Resource("/answers/{questionId}")
            class Answers(val forms: Forms, val questionId: String) {
                @Resource("/blob/{rowId?}") //optional path mapping needs to be last
                class Blob(val answer: Answers, val rowId: String? = null)
            }
        }
    }
}

fun Route.workspaceEndpoints(
    workspaceService: WorkspaceService,
) {
    get<Workspaces> {
        val w = workspaceService.findAll()
        call.respond(w)
    }

    get<Workspaces.Search> {
        val pageRequest = call.request.buildPageRequest(WorkspaceRepository.SORTABLE_FIELDS)
        val searchCriteria = WorkspaceSearchCriteria.fromRequest(call.request.queryParameters)
        val w = workspaceService.search(pageRequest, searchCriteria)
        call.respond(w)
    }

    get<Workspaces.Id> { wId ->
        val w = workspaceService.get(wId.id)
        call.respond(w)
    }

    delete<Workspaces.Id> { wId ->
        val w = workspaceService.delete(wId.id)
        call.respond(w)
    }

    get<Workspaces.Key> { wKey ->
        val w = workspaceService.get(wKey.key)
        call.respond(w)
    }

    put<Workspaces.Id> {
        val workspace = call.receive<Workspace.ForUpdate>()
        assert(workspace.id == it.id)
        val w = workspaceService.update(workspace)
        call.respond(w)
    }

    patch<Workspaces.Id> {
        val workspace = call.receive<Workspace.ForUpdateDetails>()
        assert(workspace.id == it.id)
        val w = workspaceService.updateDetails(workspace)
        call.respond(w)
    }

    post<Workspaces> {
        val workspace = call.receive<Workspace.ForCreate>()
        val w = workspaceService.create(workspace)
        call.respond(w)
    }

    post<Workspaces.Id.ImportConfigurations> { workspaceParams ->
        val workspaceId = workspaceParams.parent.id
        val importConfigurationRequestBody = call.receive<ImportConfigurationRequestBody>()

        val response =
            workspaceService.importWorkspaceConfiguration(workspaceId, importConfigurationRequestBody.importContent)
        call.respond(response)
    }


    get<Workspaces.Id.SearchFormsAndFoundations> { search ->
        val queryParams = GlobalSearchParams.fromRequest(call.request.queryParameters)
        val workspaceId = search.workspace.id
        val foundationPageRequest = call.request.buildPageRequest(FoundationRepository.SORTABLE_FIELDS)
        val formPageRequest = call.request.buildPageRequest(FormRepository.SORTABLE_FIELDS)
        val result = workspaceService.globalSearchFormsAndFoundations(
            queryParams,
            workspaceId,
            formPageRequest,
            foundationPageRequest
        )
        call.respond(result)
    }
}

fun GlobalSearchParams.Companion.fromRequest(
    parameters: Parameters
): GlobalSearchParams {
    return GlobalSearchParams(
        keywords = parameters["keywords"] ?: ""
    )
}


fun WorkspaceSearchCriteria.Companion.fromRequest(
    parameters: Parameters
): WorkspaceSearchCriteria {
    val searchTerm = parameters["search"] ?: ""

    return WorkspaceSearchCriteria(
        searchTerm = searchTerm,
    )

}

@Serializable
data class ImportConfigurationRequestBody(
    val importContent: String
)
