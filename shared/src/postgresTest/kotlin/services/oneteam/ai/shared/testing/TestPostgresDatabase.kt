package services.oneteam.ai.shared.testing

import org.jetbrains.exposed.sql.transactions.TransactionManager
import org.jetbrains.exposed.sql.transactions.transaction
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.testcontainers.containers.PostgreSQLContainer
import services.oneteam.ai.shared.*
import services.oneteam.ai.shared.database.DatabaseLive
import services.oneteam.ai.shared.database.DatabaseUtils
import java.util.concurrent.atomic.AtomicLong

/**
 * Provides a dockerized test postgres container for use in tests.
 * It will start the container and run the migrations on start up.
 *
 * TODO Add capability to set coroutine context so RLS can be tested.
 * TODO Can we mirror user permissions as per reality?
 */

// https://vuongdang.dev/articles/database-testing-with-testcontainers-and-kotlin-exposed
object TestPostgresDatabase : TestDb {
    val logger: Logger = LoggerFactory.getLogger(javaClass)
    val counter = AtomicLong(1)

    private val container: PostgreSQLContainer<Nothing> = PostgreSQLContainer<Nothing>("postgres:16-alpine").apply {

        withDatabaseName("otai-dev-database")
        withUsername("test-user")
        withPassword("test-password")
        withInitScript("db-configure.sql")

        /*
         This configures a static (instead of random) port number,
         allowing you to easily connect and inspect the db locally if needed
         (set a breakpoint in your test and connect your db tool to ${localPort})

        import com.github.dockerjava.api.model.ExposedPort
        import com.github.dockerjava.api.model.HostConfig
        import com.github.dockerjava.api.model.PortBinding
        import com.github.dockerjava.api.model.Ports

        val containerPort = 5432
        val localPort = 54320

        withExposedPorts(containerPort)
        withCreateContainerCmdModifier { cmd ->
            cmd.withHostConfig(
                HostConfig().withPortBindings(
                    PortBinding(
                        Ports.Binding.bindPort(localPort),
                        ExposedPort(containerPort)
                    )
                )
            )
        }

        */

        start()
    }

    override fun migrate() {
        logger.info("Migrating database")
        FlywayMigration().migrate(
            FlywayConfig(
                true,
                listOf(
                    "classpath:db/migration/common",
                    "classpath:db/migration/local",
                    "classpath:db/migration/postgres-only"
                ),
                mapOf("DB_USER" to "otai_admin", "DB_SUPER_USER" to "otai_superuser"),
            ), database.privileged.dataSource
        )
        DatabaseVerification("public", database).ensureTenantIsolation()
    }

    override fun drop() {
        logger.info("Dropping all tables")
        transaction(database.connectSuperUser()) {
            val conn = TransactionManager.current().connection

            DatabaseUtils.listTables("public").forEach {
                val statement = conn.prepareStatement("DROP TABLE IF EXISTS $it CASCADE", false)
                statement.executeUpdate()
            }
        }
    }

    override val database = DatabaseLive(
        DatabaseConfig(
            true,
            DBConnectionConfig(
                10,
                container.jdbcUrl,
                "org.postgresql.Driver",
                "otai_admin",
                "otai_admin",
                poolName = "test-db",
                rls = true
            ),
            // TODO should this use another user, different from the other connection?
            DBConnectionConfig(
                10,
                container.jdbcUrl,
                "org.postgresql.Driver",
                "otai_superuser",
                "otai_superuser",
                poolName = "test-db",
                rls = true
            ),
        )
    )

    init {
        start()
    }

    fun start() {
        println("Starting test database ${container.jdbcUrl}")
        drop()
        ExposedInitializer().init(true, true, database)

    }

    fun stop() {
        println("Stopping test database")
        container.stop()
    }

}