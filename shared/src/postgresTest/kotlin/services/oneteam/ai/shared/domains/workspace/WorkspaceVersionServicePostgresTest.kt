package services.oneteam.ai.shared.domains.workspace


import io.kotest.common.runBlocking
import io.kotest.matchers.comparables.shouldBeGreaterThan
import io.kotest.matchers.shouldBe
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import services.oneteam.ai.shared.Checks
import services.oneteam.ai.shared.Fixtures
import services.oneteam.ai.shared.MockHttpClient
import services.oneteam.ai.shared.domains.BadRequestException
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationService
import services.oneteam.ai.shared.domains.proxy.ExternalProxyService
import services.oneteam.ai.shared.domains.workspace.document.ApiDocumentService
import services.oneteam.ai.shared.domains.workspace.validation.*
import services.oneteam.ai.shared.middlewares.RequestContext
import services.oneteam.ai.shared.testing.TestPostgresDatabase
import kotlin.test.assertNotNull

class WorkspaceVersionServicePostgresTest {
    private val database = TestPostgresDatabase
    private val workspaceRepository = WorkspaceRepository(Checks())
    private val mockHttpClient = MockHttpClient()
    private val proxyService = ExternalProxyService("token", mockHttpClient.client)
    private val documentService = ApiDocumentService(
        proxyService,
        mock(FlowStepTypeConfigurationService::class.java)

    )
    private val workspaceVersionService =
        WorkspaceVersionService(workspaceRepository, documentService, WorkspaceVersionRepository(), Checks())

    private val workspaceText =
        this::class.java.getResource("/sample-data/sgpit/sgpit-workspace-config.json")!!.readText()
    private val workspaceForJson = Json.decodeFromString(
        Workspace.ForJson.serializer(),
        workspaceText
    )
    private val workspaceForJsonWithErrors = workspaceForJson.copy(
        errors = listOf(
            ConstraintError(
                key = Field("key"),
                type = Type("type"),
                path = Path("path"),
                constraintDetail = ConstraintDetail("detail"),
                message = Message("error")
            )
        )
    )

    private fun getJson(): Json {
        return Json(builderAction = {
            prettyPrint = true
            encodeDefaults = true
            isLenient = true
            ignoreUnknownKeys = true
        })
    }

    @Test
    fun `should create a workspace version`() {
        runBlocking {
            val fixtures = Fixtures(database).initialise()

            withContext(RequestContext(tenant = fixtures.tenant2)) {
                // prepare
                val workspace = workspaceRepository.create(
                    Workspace.ForCreate(
                        Workspace.Name("testWorkspace"), Workspace.Key("T1"),
                        Workspace.Description("test description"),
                        Workspace.DocumentId("abc")
                    )
                )
                mockHttpClient.documents[workspace.documentId!!] = workspaceText
                assertNotNull(workspace)

                // perform
                val version = workspaceVersionService.createVersion(Workspace.Id(workspace.id.value), "cookie")

                // verify
                version.workspaceId shouldBe Workspace.Id(workspace.id.value)
                version.id.value shouldBeGreaterThan (0L)
                version.configuration shouldBe workspaceForJson
            }
        }
    }

    @Test
    fun `should not create a workspace version when errors exist`() {
        runBlocking {
            val fixtures = Fixtures(database).initialise()
            withContext(RequestContext(tenant = fixtures.tenant2)) {
                // prepare
                val workspace = workspaceRepository.create(
                    Workspace.ForCreate(
                        Workspace.Name("testWorkspace"), Workspace.Key("T1"),
                        Workspace.Description("test description"),
                        Workspace.DocumentId("abc")
                    )
                )
                val encodeToString =
                    getJson().encodeToString(Workspace.ForJson.serializer(), workspaceForJsonWithErrors)
                mockHttpClient.documents[workspace.documentId!!] = encodeToString
                assertNotNull(workspace)

                // perform
                assertThrows(BadRequestException::class.java) {
                    kotlinx.coroutines.runBlocking {
                        withContext(RequestContext(tenant = fixtures.tenant2)) {
                            workspaceVersionService.createVersion(Workspace.Id(workspace.id.value), "cookie")
                        }
                    }
                }

            }
        }
    }
}