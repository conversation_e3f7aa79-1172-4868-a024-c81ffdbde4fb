package services.oneteam.ai.shared

import kotlinx.serialization.json.Json
import org.jetbrains.exposed.sql.transactions.transaction
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationEntity
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepType
import services.oneteam.ai.shared.testing.TestPostgresDatabase
import kotlin.test.*

class DataSeedTest {
    private val databaseContainer = TestPostgresDatabase
    private val seeder = DataSeeder(databaseContainer.database)
    val fixtures = Fixtures(databaseContainer).initialise()

    @Test
    fun `should seed table from json`() {
        transaction(databaseContainer.database.connectSuperUser())
        {
            val filePath = "/DataSeedTest.json"

            seeder.seed<FlowStepType, FlowStepTypeConfigurationEntity>(filePath)

            val seedData = Json.decodeFromString<List<FlowStepType>>(
                this::class.java.getResource(filePath)!!.readText()
            ) as List<Seedable<FlowStepTypeConfigurationEntity>>

            seedData.forEach {
                assertNotNull(it.getRelatedEntity().find(it.getLocatorPredicate()).firstOrNull())
            }
        }
    }

    @Test
    fun `should update seeded record`() {
        transaction(databaseContainer.database.connectSuperUser())
        {
            val filePath = "/DataSeedTest.json"
            val updatedFilePath = "/DataSeedTest2.json"

            val originalJson = Json.decodeFromString<List<FlowStepType>>(
                this::class.java.getResource(filePath)!!.readText()
            ) as List<Seedable<FlowStepTypeConfigurationEntity>>

            seeder.seed<FlowStepType, FlowStepTypeConfigurationEntity>(filePath)

            seeder.seed<FlowStepType, FlowStepTypeConfigurationEntity>(updatedFilePath)

            originalJson.forEach { assertFalse { it.compareObjectWithEntity(it.getRelatedEntity().find { it.getLocatorPredicate() }.first()) } }
        }
    }

//    @Test
//    fun `should not update seeded record`() {
//        transaction(databaseContainer.database.connectSuperUser())
//        {
//            val filePath = "/DataSeedTest.json"
//
//            val originalJson = Json.decodeFromString<List<FlowStepTypeConfigurationDTO>>(
//                this::class.java.getResource(filePath)!!.readText()
//            ) as List<Seedable<FlowStepTypeConfigurationEntity>>
//
//            seeder.seed<FlowStepTypeConfigurationDTO, FlowStepTypeConfigurationEntity>(filePath)
//
//            seeder.seed<FlowStepTypeConfigurationDTO, FlowStepTypeConfigurationEntity>(filePath)
//
//            originalJson.forEach { assertTrue { it.compareObjectWithEntity(it.getRelatedEntity().find { it.getLocatorPredicate() }.first()) } }
//        }
//    }

    @Test
    fun `chained seeding test`() {
        transaction(databaseContainer.database.connectSuperUser())
        {
            val filePath = "/DataSeedTest.json"
            val secondFilePath = "/ChainedDataSeedTest.json"

            val json1 = Json.decodeFromString<List<FlowStepType>>(
                this::class.java.getResource(filePath)!!.readText()
            ) as List<Seedable<FlowStepTypeConfigurationEntity>>

            val json2 = Json.decodeFromString<List<FlowStepType>>(
                this::class.java.getResource(secondFilePath)!!.readText()
            ) as List<Seedable<FlowStepTypeConfigurationEntity>>

            seeder.seed<FlowStepType, FlowStepTypeConfigurationEntity>(filePath)
                .seed<FlowStepType, FlowStepTypeConfigurationEntity>(secondFilePath)

            json1.forEach {
                assertNotNull(it.getRelatedEntity().find(it.getLocatorPredicate()).firstOrNull())
            }

            json2.forEach {
                assertNotNull(it.getRelatedEntity().find(it.getLocatorPredicate()).firstOrNull())
            }
        }
    }
}
