package services.oneteam.ai.shared

import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.decodeFromStream
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.domains.collection.form.BlobService
import services.oneteam.ai.shared.domains.collection.form.FormRepository
import services.oneteam.ai.shared.domains.collection.form.FormService
import services.oneteam.ai.shared.domains.collection.foundation.FoundationRepository
import services.oneteam.ai.shared.domains.collection.foundation.FoundationService
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepType
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfiguration
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationService
import services.oneteam.ai.shared.domains.proxy.ExternalProxyService
import services.oneteam.ai.shared.domains.tenant.Tenant
import services.oneteam.ai.shared.domains.tenant.TenantRepository
import services.oneteam.ai.shared.domains.workspace.FoundationConfigurationService
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceRepository
import services.oneteam.ai.shared.domains.workspace.WorkspaceService
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersionRepository
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersionService
import services.oneteam.ai.shared.domains.workspace.validation.WorkspaceValidationContext
import services.oneteam.ai.shared.middlewares.RequestContext
import java.nio.file.Paths
import java.util.*

class Fixtures(val testDb: TestDb) {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    val checks = Checks()
    val d = ResourceBundle.getBundle("dictionary")

    val proxyService = ExternalProxyService("token", MockHttpClient().client)
    val dictionary = ResourceBundle.getBundle("dictionary")
    val tenantRepository = TenantRepository(testDb.database)
    val workspaceRepository = WorkspaceRepository(checks)
    val foundationRepository = FoundationRepository()

    val foundationConfigurationService = FoundationConfigurationService(checks, dictionary, foundationRepository)
    val flowStepTypeConfigurationRepository =
        services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationRepository()
    val flowStepTypeConfigurationService = FlowStepTypeConfigurationService(flowStepTypeConfigurationRepository)
    val formRepository = FormRepository(checks)
    val documentService = services.oneteam.ai.shared.domains.workspace.document.ApiDocumentService(
        proxyService,
        flowStepTypeConfigurationService
    )
    val workspaceVersionRepository = WorkspaceVersionRepository()
    val workspaceVersionService = WorkspaceVersionService(workspaceRepository, documentService,workspaceVersionRepository, checks )
    val foundationService = FoundationService(foundationRepository, workspaceVersionService, checks)
    val uploadService = BlobService("","","")
    val formService = FormService(
        formRepository,
        workspaceRepository,
        documentService,
        workspaceVersionRepository,
        uploadService,
        foundationService,
        workspaceVersionService,
        checks
    )
    val workspaceService = WorkspaceService(
        workspaceRepository,
        foundationService,
        documentService,
        foundationConfigurationService,
        workspaceVersionService,
        formService,
        checks
    )

    lateinit var tenant1: Tenant
    lateinit var tenant2: Tenant

    val Workspace1_Key = "WS1"
    val Workspace2_Key = "WS2"

    fun initialise(): Fixtures {
        logger.info("Initialising fixtures")

        testDb.drop()
        testDb.migrate()

        // these represent the tenants inserted via migrations in src/postgresTest/resources/db/migration/local/V20241121.000001__seed.sql and src/h2Test/resources/db/migration/local/V20241121.000001__seed.sql
        tenant1 = tenantRepository.getByOriginUrl("http://tenant1")!!
        tenant2 = tenantRepository.getByOriginUrl("http://tenant2")!!

        runBlocking {
            withContext(RequestContext(tenant = tenant1)) {
                workspaceService.create(
                    Workspace.ForCreate(
                        Workspace.Name("testWorkspace1"), Workspace.Key(Workspace1_Key),
                        Workspace.Description("test description")
                    )
                )
            }
        }
        return this
    }

    companion object {
        fun stepTypeConfigurations(): Map<String, FlowStepTypeConfiguration> {
            return Json.decodeFromStream<List<FlowStepType>>(this::class.java.getResourceAsStream("/StepTypeSeed.json")!!)
                .map {
                    FlowStepTypeConfiguration(
                        id = 0,
                        primaryIdentifier = it.primaryIdentifier,
                        type = it.type,
                        tenantId = 0,
                        name = it.name,
                        description = it.description,
                        properties = it.properties
                    )
                }
                .associateBy { it.primaryIdentifier }

        }

        fun helperFlows(): Map<FlowConfiguration.Id, FlowConfiguration.ForJson> {
            val file = Paths.get("src/test/resources/validation/flowconfiguration/valid/helper-flow-set-variable.json").toFile()
            val input = file.readText()
            var flowConfiguration = Json.decodeFromString<FlowConfiguration.ForJson>(input)
            return mapOf(
                flowConfiguration.id to flowConfiguration
            )
        }
    }
}