package services.oneteam.ai.shared.domains.workspace

import org.jetbrains.exposed.sql.Query
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.andWhere
import org.jetbrains.exposed.sql.lowerCase
import org.jetbrains.exposed.sql.selectAll
import services.oneteam.ai.shared.Checks
import services.oneteam.ai.shared.database.DatabaseUtils
import services.oneteam.ai.shared.domains.Page
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.SortableFields
import services.oneteam.ai.shared.extensions.paginate

data class WorkspaceSearchCriteria(
    val searchTerm: String? // search by name
) {
    companion object {
        // empty companion object so we can add extension functions
    }
}

class WorkspaceRepository(private val checks: Checks) {

    companion object {
        val SORTABLE_FIELDS = SortableFields(
            mapOf(
                "key" to Workspaces.key,
                "name" to Workspaces.name,
                "id" to Workspaces.id
            )
        )
    }

    suspend fun findOne(id: Long): WorkspaceEntity {
        return checks.exists(getById(id)) { "Workspace $id not found" }
    }

    suspend fun getById(id: Long): WorkspaceEntity? {
        return DatabaseUtils.dbQueryWithTenant {
            val dao = WorkspaceEntity.find {Workspaces.id eq id and (Workspaces.deleted eq false) }.singleOrNull()
            return@dbQueryWithTenant dao
        }
    }

    suspend fun getByKey(key: String): WorkspaceEntity? {
        return DatabaseUtils.dbQueryWithTenant {
            val dao = WorkspaceEntity.find { Workspaces.key eq key and (Workspaces.deleted eq false) }.singleOrNull()
            return@dbQueryWithTenant dao
        }
    }

    suspend fun create(workspace: Workspace.ForCreate): WorkspaceEntity {
        return DatabaseUtils.dbQueryWithTenant { tenant ->
            val dao = WorkspaceEntity.new {
                name = workspace.name.value
                key = workspace.key.value
                description = workspace.description?.value
                documentId = workspace.documentId?.value
                tenantId = tenant.id
            }
            return@dbQueryWithTenant dao
        }
    }

    suspend fun update(workspace: Workspace.ForUpdate): WorkspaceEntity {
        return DatabaseUtils.dbQueryWithTenant {
            val dao = WorkspaceEntity.findByIdAndUpdate(workspace.id.value) {
                it.name = workspace.name.value
                it.documentId = workspace.documentId?.value
                it.description = workspace.description?.value
            }
            return@dbQueryWithTenant dao!!
        }
    }

    suspend fun updateDetails(workspace: Workspace.ForUpdateDetails): WorkspaceEntity {
        return DatabaseUtils.dbQueryWithTenant {
            val dao = WorkspaceEntity.findByIdAndUpdate(workspace.id.value) {
                it.name = workspace.name.value
                it.description = workspace.description?.value
            }
            return@dbQueryWithTenant dao!!
        }
    }

    suspend fun delete(id: Workspace.Id) {
        return DatabaseUtils.dbQueryWithTenant {
            WorkspaceEntity.findByIdAndUpdate(id.value) {
                it.deleted = true
            }
        }
    }

    suspend fun getAll(): List<WorkspaceEntity> = DatabaseUtils.dbQueryWithTenant {
        //make the iterator non-lazy
        return@dbQueryWithTenant WorkspaceEntity.find { Workspaces.deleted eq false }.map { it }
    }

    suspend fun searchByCriteria(pageRequest: PageRequest, searchCriteria: WorkspaceSearchCriteria): Page<WorkspaceEntity> {
        return DatabaseUtils.dbQueryWithTenant {
            return@dbQueryWithTenant searchCriteria.toQuery().paginate(pageRequest, SORTABLE_FIELDS) {
                WorkspaceEntity.wrapRow(
                    it
                )
            }
        }
    }
}

fun WorkspaceSearchCriteria.toQuery(): Query {
    val query = Workspaces.selectAll().andWhere { Workspaces.deleted eq false }
    if (searchTerm?.isNotBlank() == true) {
        query.andWhere { Workspaces.name.lowerCase() like "%${searchTerm.lowercase()}%" }
    }

    return query
}
