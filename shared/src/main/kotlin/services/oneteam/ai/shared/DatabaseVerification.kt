package services.oneteam.ai.shared

import org.jetbrains.exposed.sql.transactions.transaction
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.database.DBInterface
import services.oneteam.ai.shared.database.DatabaseUtils

/**
 * Checks to ensure state of database is correct.
 */
class DatabaseVerification(val schemaName: String, private val database: DBInterface) {
    private val excludeTableFromRLS = listOf<String>("flyway_schema_history", "tenants")
    val logger: Logger = LoggerFactory.getLogger(javaClass)
    /**
     * Adds tenant_isolation_policy to tables that don't have it (excluding those in excludeTableFromRLS).
     */
    fun ensureTenantIsolation() {
        logger.info("Verifying database schema $schemaName")
        transaction(database.connectSuperUser()) {
            DatabaseUtils.listTables("public").filter { !excludeTableFromRLS.contains(it) }.forEach { table ->
                // create policy
                DatabaseUtils.isPolicySetOnTable(table, "tenant_isolation_policy").let { hasPolicy ->
                    if (!hasPolicy) {
                        logger.info("tenant_isolation_policy not set on table $table. Setting...")
                        DatabaseUtils.enableRowLevelSecurityPolicyForSchema(table)
                    } else {
                        logger.info("tenant_isolation_policy is already set on table $table")
                    }
                }
                // enable RLS
                DatabaseUtils.isRLSEnabled(table).let { rlsEnabled ->
                    if (!rlsEnabled) {
                        logger.info("RLS not enabled on table $table. Enabling...")
                        DatabaseUtils.enableRowLevelSecurityForTable(table)
                    } else {
                        logger.info("RLS is already enabled on table $table")
                    }
                }
            }
            logger.info("Database schema $schemaName status:")
            val status = DatabaseUtils.printRLS(schemaName)
            status.filter { it.second }.joinToString("\n").let { logger.info("Tables with RLS: \n$it") }
            status.filter { !it.second }.joinToString("\n").let { logger.info("Tables WITHOUT RLS: \n$it") }
        }
    }

}