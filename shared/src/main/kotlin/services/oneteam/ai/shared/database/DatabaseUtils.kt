package services.oneteam.ai.shared.database

import org.jetbrains.exposed.dao.Entity
import org.jetbrains.exposed.dao.EntityClass
import org.jetbrains.exposed.sql.IntegerColumnType
import org.jetbrains.exposed.sql.ResultRow
import org.jetbrains.exposed.sql.transactions.TransactionManager
import org.jetbrains.exposed.sql.transactions.experimental.newSuspendedTransaction
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.domains.tenant.Tenant
import services.oneteam.ai.shared.extensions.fieldsIndexMap
import services.oneteam.ai.shared.extensions.setValueOrNull
import services.oneteam.ai.shared.middlewares.RequestContext
import kotlin.coroutines.coroutineContext

const val RLS_TENANT_KEY = "app.current_tenant_id"
const val PRINCIPAL_KEY = "app.current_principal_id"

object DatabaseUtils {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    /**
     * Sets the tenant and principal for the current transaction.
     * coroutineContext[RequestContext]!!.tenant must be defined
     *
     * See https://www.postgresql.org/docs/16/functions-admin.html#FUNCTIONS-ADMIN-SET
     * Can be retrieved in queries using
     *    SELECT CURRENT_SETTING('app.current_principal_id', TRUE)
     *
     */
    suspend fun <T> dbQueryWithTenant(block: suspend (tenant: Tenant) -> T): T {
        logger.trace("dbQueryWithTenant: {}", coroutineContext[RequestContext])
        try {
            val tenant = coroutineContext[RequestContext]!!.tenant
            // Principal may be null for operations performed by the system - or, do we want to enforce a principal and supply a system user?
            val principalId = coroutineContext[RequestContext]?.principalId()

            return newSuspendedTransaction {
                val conn = TransactionManager.current().connection
                val query =
                    "SELECT set_config('$RLS_TENANT_KEY', ?::text, TRUE), set_config('$PRINCIPAL_KEY', ?::text, TRUE)"

                val statement = conn.prepareStatement(query, false)
                statement[1] = tenant.id
                statement.setValueOrNull(2, principalId, IntegerColumnType())

                statement.executeQuery()
                return@newSuspendedTransaction block(tenant)
            }
        } catch (e: Exception) {
            e.printStackTrace()
            throw e
        }
    }

    fun <T : Entity<*>> runPreparedStatementList(
        sql: String,
        parameters: List<*>,
        table: BaseLongIdTable,
        entity: EntityClass<*, T>
    ): List<T> {

        val conn = TransactionManager.current().connection
        val statement = conn.prepareStatement(sql, false)
        parameters.forEachIndexed { index, value ->
            statement.setValueOrNull(index + 1, value, table.columns[index].columnType)
        }
        val resultSet = statement.executeQuery()

        val fieldsIndex = table.fieldsIndexMap()
        val results = mutableListOf<T>()
        while (resultSet.next()) {
            results.add(entity.wrapRow(ResultRow.create(resultSet, fieldsIndex)))
        }
        return results
    }

    fun enableRowLevelSecurityPolicyForSchema(tableName: String) {
        val conn = TransactionManager.current().connection

        val sql =
            "CREATE POLICY tenant_isolation_policy ON \"$tableName\" USING (\"$tableName\".\"tenant_id\" = current_setting('$RLS_TENANT_KEY', TRUE)::int);"
        val statement = conn.prepareStatement(sql, false)
        statement.executeUpdate()
    }

    fun enableRowLevelSecurityForTable(tableName: String) {
        val conn = TransactionManager.current().connection
        val sql = "ALTER TABLE \"$tableName\" ENABLE ROW LEVEL SECURITY;"
        val statement = conn.prepareStatement(sql, false)
        statement.executeUpdate()
    }

    fun isRLSEnabled(tableName: String): Boolean {
        val conn = TransactionManager.current().connection
        val query = """
            SELECT relrowsecurity
            FROM pg_class
            WHERE relname = ?;
        """
        val statement = conn.prepareStatement(query, false)
        statement[1] = tableName
        val resultSet = statement.executeQuery()
        return if (resultSet.next()) {
            resultSet.getBoolean(1)
        } else {
            false
        }
    }

    fun isPolicySetOnTable(tableName: String, policyName: String): Boolean {
        val conn = TransactionManager.current().connection
        val query = """
            SELECT EXISTS (
                SELECT 1
                FROM pg_policies
                WHERE tablename = ? AND policyname = ?
            )
        """
        val statement = conn.prepareStatement(query, false)
        statement[1] = tableName
        statement[2] = policyName
        val resultSet = statement.executeQuery()
        return if (resultSet.next()) {
            resultSet.getBoolean(1)
        } else {
            false
        }
    }

    fun listTables(schemaName: String): List<String> {
        val conn = TransactionManager.current().connection
        val query = """
            SELECT tablename FROM pg_catalog.pg_tables WHERE schemaname=?;
        """
        val statement = conn.prepareStatement(query, false)
        statement[1] = schemaName
        val resultSet = statement.executeQuery()
        return resultSet.use {
            generateSequence {
                if (resultSet.next()) resultSet.getString(1) else null
            }.toList()
        }
    }

    fun printRLS(schemaName: String): List<Pair<String, Boolean>> {
        val conn = TransactionManager.current().connection
        val query = """
            SELECT relname, relrowsecurity, relforcerowsecurity
            FROM pg_class
            WHERE relnamespace = 'public'::regnamespace
              AND relname IN
                  (SELECT table_name FROM information_schema.tables WHERE table_schema = '${schemaName}' AND table_type = 'BASE TABLE');
        """
        val statement = conn.prepareStatement(query, false)
        val resultSet = statement.executeQuery()
        return resultSet.use {
            generateSequence {
                if (resultSet.next()) Pair(resultSet.getString(1), resultSet.getBoolean(2)) else null
            }.toList()
        }
    }
}

