package services.oneteam.ai.shared.domains.workspace

import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import org.jetbrains.exposed.sql.transactions.experimental.newSuspendedTransaction
import services.oneteam.ai.shared.Checks
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.Page
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.collection.form.Form
import services.oneteam.ai.shared.domains.collection.form.FormService
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.collection.foundation.FoundationService
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.workspace.document.IDocumentService
import services.oneteam.ai.shared.domains.workspace.document.create
import java.time.Instant

/**
 * Service for managing workspaces.
 *
 * This service will always look up the workspace by its key to ensure it exists,
 * and will throw NotFoundException if it does not, ensuring a 404 response is returned
 * when an invalid workspace is referenced.
 */
class WorkspaceService(
    private val workspaceRepository: WorkspaceRepository,
    private val foundationService: FoundationService,
    private val documentService: IDocumentService,
    private val foundationConfigurationService: FoundationConfigurationService,
    private val workspaceVersionService: WorkspaceVersionService,
    private val formService: FormService,
    val check: Checks
) {

    suspend fun findAll(): List<Workspace.ForApi> {
        return workspaceRepository.getAll().map { it.toDTO() }
    }

    suspend fun search(
        pageRequest: PageRequest, workspaceSearchCriteria: WorkspaceSearchCriteria
    ): Page<Workspace.ForApi> {
        val searchResult = workspaceRepository.searchByCriteria(
            pageRequest, workspaceSearchCriteria
        )
        return Page(
            searchResult.page, searchResult.total, searchResult.items.map { it.toDTO() })
    }

    /**
     * Create a new workspace with a default document.
     */
    suspend fun create(
        workspace: Workspace.ForCreate,
    ): Workspace.ForApi {
        val workspaceForJson = createDefaultWorkspaceDocument()
        return create(workspace, workspaceForJson)
    }

    /**
     * Create a new workspace with a given document.
     */
    suspend fun create(
        workspace: Workspace.ForCreate,
        workspaceForJson: Workspace.ForJson,
        timeoutMillis: Long? = null
    ): Workspace.ForApi {

        return newSuspendedTransaction {
            val createdWorkspace = workspaceRepository.create(workspace).toDTO()
            // create document
            val documentId = documentService.create(
                workspaceForJson.copy(
                    id = createdWorkspace.id,
                    name = createdWorkspace.name,
                    key = createdWorkspace.key,
                    description = createdWorkspace.description
                ),
                timeoutMillis = timeoutMillis
            )
            // set document id in workspace entity
            val updated = workspaceRepository.update(
                Workspace.ForUpdate(
                    id = createdWorkspace.id,
                    name = createdWorkspace.name,
                    key = createdWorkspace.key,
                    description = createdWorkspace.description,
                    documentId = Workspace.DocumentId(documentId),
                    configuration = workspaceForJson
                )
            )
            // create foundation with name and key of the workspace and link it to the root foundation configuration
            foundationService.create(
                Foundation.ForCreate(
                    name = Foundation.Name(createdWorkspace.name.value),
                    key = Foundation.Key(createdWorkspace.key.value.uppercase()),
                    workspaceId = createdWorkspace.id,
                    parentId = null,
                    foundationConfigurationId = workspaceForJson.foundations.order[0]
                ),
                skipValidation = true
            )
            return@newSuspendedTransaction updated.toDTO()
        }

    }

    /**
     * Create a template workspace document - needs to be updated with workspace entity details.
     */
    fun createDefaultWorkspaceDocument(): Workspace.ForJson {
        val rootFoundationConfiguration = foundationConfigurationService.createRootFoundationConfiguration()
        val workspaceForJson = Workspace.ForJson(
            id = Workspace.Id(0),
            name = Workspace.Name(""),
            key = Workspace.Key(""),
            description = Workspace.Description(""),
            foundations = OrderedMap(listOf(rootFoundationConfiguration)),
            forms = emptyMap(),
            flows = OrderedMap<FlowConfiguration.Id, FlowConfiguration.ForJson>(emptyList()),
            series = emptyMap(),
            labels = emptyMap(),
            errors = emptyList(),
            metadata = EntityMetadata(Instant.now(), Instant.now())
        )
        return workspaceForJson
    }

    suspend fun update(workspace: Workspace.ForUpdate): Workspace.ForApi {
        workspaceRepository.findOne(workspace.id.value)
        return workspaceRepository.update(workspace).toDTO()
    }

    suspend fun updateDetails(workspace: Workspace.ForUpdateDetails): Workspace.ForApi {
        workspaceRepository.findOne(workspace.id.value)
        return workspaceRepository.updateDetails(workspace).toDTO()
    }

    suspend fun delete(id: Workspace.Id) {
        workspaceRepository.findOne(id.value)
        workspaceRepository.delete(id)
    }

    suspend fun get(id: Workspace.Id): Workspace.ForApi {
        val entity = workspaceRepository.findOne(id.value)
        return entity.toDTO()
    }

    suspend fun get(key: String): Workspace.ForApi {
        val entity = checkExistsKey(key)
        return entity.toDTO()
    }

    private suspend fun checkExistsKey(key: String): WorkspaceEntity {
        val entity = workspaceRepository.getByKey(key)
        return check.exists(entity) { "Unknown workspace key $key" }
    }

    suspend fun importWorkspaceConfiguration(id: Workspace.Id, importConfig: String): String {
        val workspace = get(id)
        if (workspace.documentId?.value == null) {
            throw IllegalArgumentException("Workspace document not found")
        }

        val json = Json {
            ignoreUnknownKeys = true
            encodeDefaults = true
        }

        val parsedContent = json.decodeFromString<Workspace.ForJson>(importConfig).copy(
            id = Workspace.Id(workspace.id.value),
            key = Workspace.Key(workspace.key.value),
            name = Workspace.Name(workspace.name.value),
        )

        val foundationIdToReplace = parsedContent.foundations.order[0]
        val workspaceConfigurationFoundationIdToKeep = (foundationService.root(id)).foundationConfigurationId

        val newContent = json.encodeToString(Workspace.ForJson.serializer(), parsedContent)
            .replace(foundationIdToReplace.value, workspaceConfigurationFoundationIdToKeep.value);
        val parsedNewContent = json.decodeFromString<Workspace.ForJson>(newContent)

        return documentService.update(
            workspace.documentId.value, null, parsedNewContent, "", Workspace.ForJson::class
        )
    }

    suspend fun globalSearchFormsAndFoundations(
        queryParams: GlobalSearchParams,
        workspaceId: Workspace.Id,
        formPageRequest: PageRequest,
        foundationPageRequest: PageRequest
    ): CombinedSearchResult {
        val keywords = queryParams.keywords
        if (keywords.isEmpty()) {
            return CombinedSearchResult(
                foundations = emptyList(),
                forms = emptyList()
            )
        }
        val workspaceConfigurationResults = workspaceVersionService.searchConfigByKeywords(workspaceId, keywords)
        val matchedFoundations = foundationService.searchByKeywords(
            workspaceId, foundationPageRequest, keywords, workspaceConfigurationResults.matchedFoundationConfigs
        )
        var matchedForms: List<Form.ForApi> = emptyList()
        if (!workspaceConfigurationResults.matchedFormConfigs.isEmpty() || !workspaceConfigurationResults.matchedIntervals.isEmpty() || keywords.isNotEmpty()) {
            val forms = formService.searchByConfigId(
                workspaceId,
                formPageRequest,
                workspaceConfigurationResults.matchedFormConfigs,
                workspaceConfigurationResults.matchedIntervals,
                keywords
            )
            matchedForms = forms.items
        }
        return CombinedSearchResult(
            matchedFoundations.items, matchedForms
        )

    }

}

fun WorkspaceEntity.toDTO(): Workspace.ForApi {
    return Workspace.ForApi(
        id = Workspace.Id(this.id.value),
        name = Workspace.Name(this.name),
        key = Workspace.Key(this.key),
        description = if (this.description == null) null else Workspace.Description(this.description!!),
        documentId = if (this.documentId == null) null else Workspace.DocumentId(this.documentId!!),
        metadata = EntityMetadata.from(this)
    )
}

@Serializable
data class CombinedSearchResult(
    val foundations: List<Foundation.ForApi>, val forms: List<Form.ForApi>
)

@Serializable
data class GlobalSearchParams(
    val keywords: String
)
