package services.oneteam.ai.shared.domains.user

import services.oneteam.ai.shared.domains.Page
import services.oneteam.ai.shared.domains.PageRequest

class UserService(private val userRepository: UserRepository) {

    suspend fun create(user: User.ForCreate): User.ForApi {
        return userRepository.create(user).toDTO()
    }

    suspend fun getAll(): List<User.ForApi> {
        return userRepository.getAll().map { it.toDTO() }
    }

    private fun UserEntity.toDTO(): User.ForApi {
        return User.ForApi(
            id = User.Id(this.id.value),
            email = this.email,
            tenantId = this.tenantId,
            properties = this.properties
        )
    }

    suspend fun search(
        pageRequest: PageRequest, userSearchCriteria: UserSearchCriteria
    ): Page<User.ForApi> {
        val searchResult = userRepository.searchByCriteria(
            pageRequest, userSearchCriteria
        )
        return Page(
            searchResult.page, searchResult.total, searchResult.items.map { it.toDTO() })
    }
}