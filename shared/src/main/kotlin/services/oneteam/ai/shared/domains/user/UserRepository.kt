package services.oneteam.ai.shared.domains.user

import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.Checks
import services.oneteam.ai.shared.database.DatabaseUtils


class UserRepository(private val checks: Checks) {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    suspend fun findOne(id: Long): UserEntity {
        return checks.exists(getById(id)) { "User $id not found" }
    }

    suspend fun getById(id: Long): UserEntity? {
        return DatabaseUtils.dbQueryWithTenant {
            val dao = UserEntity.findById(id)
            return@dbQueryWithTenant dao
        }
    }

    suspend fun create(user: User.ForCreate): UserEntity {
        return DatabaseUtils.dbQueryWithTenant { tenant ->
            val dao = UserEntity.new {
                email = user.email
                tenantId = tenant.id
            }
            return@dbQueryWithTenant dao
        }
    }

    suspend fun updateOrCreate(user: User.ForCreate): UserEntity {
        return DatabaseUtils.dbQueryWithTenant { tenant ->
            val existingUser = UserEntity.findById(user.id.value)
            if (existingUser == null) {
                return@dbQueryWithTenant UserEntity.new(user.id.value) {
                    email = user.email
                    tenantId = tenant.id
                }
            }
            if (existingUser.email != user.email) {
                return@dbQueryWithTenant UserEntity.findByIdAndUpdate(existingUser.id.value) {
                    existingUser.email = user.email
                }!!
            }
            return@dbQueryWithTenant existingUser
        }
    }

    suspend fun getAll(): List<UserEntity> = DatabaseUtils.dbQueryWithTenant {
        //make the iterator non-lazy
        return@dbQueryWithTenant UserEntity.all().map { it }
    }

}
