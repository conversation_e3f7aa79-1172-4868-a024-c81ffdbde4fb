package services.oneteam.ai.shared.domains.workspace

import org.jetbrains.exposed.sql.Query
import org.jetbrains.exposed.sql.andWhere
import org.jetbrains.exposed.sql.json.extract
import org.jetbrains.exposed.sql.selectAll
import services.oneteam.ai.shared.database.DatabaseUtils
import services.oneteam.ai.shared.domains.Page
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.SortableFields
import services.oneteam.ai.shared.domains.user.UserEntity
import services.oneteam.ai.shared.domains.user.UserRepository
import services.oneteam.ai.shared.extensions.paginate

data class WorkspaceUserSearchCriteria(
    val workspaceId: Workspace.Id,
    val searchTerm: String?
) {
    companion object {
        // empty companion object so we can add extension functions
    }
}

class WorkspaceUserRepository(val workspaceRepository: WorkspaceRepository, val userRepository: UserRepository) {

    companion object {
        val SORTABLE_FIELDS = SortableFields(
            mapOf(
                "status" to WorkspaceUsersSchema.status,
                "userId" to WorkspaceUsersSchema.userId
            )
        )
    }

    suspend fun create(workspaceUser: WorkspaceUser.ForCreate): WorkspaceUserEntity {
        val workspace = workspaceRepository.findOne(workspaceUser.workspaceId)
        val user = userRepository.findOne(workspaceUser.userId.value)
        return DatabaseUtils.dbQueryWithTenant { tenant ->
            workspaceRepository.findOne(workspaceUser.workspaceId)
            val dao = WorkspaceUserEntity.new {
                workspaceId = workspace.id
                userId = user.id
                tenantId = tenant.id
                accessLevel = workspaceUser.accessLevel
                status = workspaceUser.status
            }
            return@dbQueryWithTenant dao
        }
    }

    suspend fun searchByCriteria(pageRequest: PageRequest, workspaceUserSearchCriteria: WorkspaceUserSearchCriteria): Page<WorkspaceUserEntity> {
        return DatabaseUtils.dbQueryWithTenant {
            return@dbQueryWithTenant workspaceUserSearchCriteria.toQuery().paginate(pageRequest, SORTABLE_FIELDS) {
                WorkspaceUserEntity.wrapRow(
                    it
                )
            }
        }
    }
}

fun WorkspaceUserSearchCriteria.toQuery(): Query {
    val query = WorkspaceUsersSchema.selectAll().andWhere { WorkspaceUsersSchema.workspaceId eq workspaceId.value }
//    if (searchTerm?.isNotBlank() == true) {
//        val lowercaseSearchTerm = "%${searchTerm.lowercase()}%"
////        query.andWhere {
////            (WorkspaceUsersSchema.userId.extract<UserEntity>().email.lowerCase() like lowercaseSearchTerm) or
////                    (WorkspaceUsersSchema.properties.extract<String>("firstName").lowerCase() like lowercaseSearchTerm) or
////                    (WorkspaceUsersSchema.properties.extract<String>("lastName").lowerCase() like lowercaseSearchTerm)
////        }
//    }

    return query
}