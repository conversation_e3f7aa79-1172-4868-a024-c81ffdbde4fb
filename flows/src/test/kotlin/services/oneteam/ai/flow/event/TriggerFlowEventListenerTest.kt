package services.oneteam.ai.flow.event

import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.mockkObject
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import org.junit.jupiter.api.Assertions.assertNotNull
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`
import services.oneteam.ai.flow.execution.*
import services.oneteam.ai.flow.execution.step.ContextToJsonObjectBuilder
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.event.Event
import services.oneteam.ai.shared.domains.event.EventKey
import services.oneteam.ai.shared.domains.event.EventStatus
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepType
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepType.Properties.Configuration
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepType.Properties.Configuration.TriggerEventSubscription
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepType.Properties.Configuration.TriggerEventSubscription.VariableMapping
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfiguration
import services.oneteam.ai.shared.domains.flow.flowStepTypeConfiguration.FlowStepTypeConfigurationService
import services.oneteam.ai.shared.domains.proxy.ProxyService
import services.oneteam.ai.shared.domains.proxy.ProxyService.ProxyEndpointResponse
import services.oneteam.ai.shared.domains.proxy.ProxyService.ProxyEndpointResponseStatus
import services.oneteam.ai.shared.domains.workspace.Workspace
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersion
import services.oneteam.ai.shared.domains.workspace.WorkspaceVersionService
import kotlin.test.Test
import kotlin.test.assertEquals


class TriggerFlowEventListenerTest {
    private val flowStepTypeConfigurationService = mock<FlowStepTypeConfigurationService>()
    private val workspaceVersionService = mock<WorkspaceVersionService>()
    private val proxyService = mockProxyService()

    val event = Event.ForJson(
        workspaceId = Workspace.Id(1), eventProperties = Event.EventProperties.StartFlowManuallyFromFormProperties(
            buttonLabel = "Submit",
            form = Event.FormMinimal(
                10, 1,
                formConfiguration = Event.FormConfigurationMinimal(
                    "formConfigurationId1", "formConfigurationKey1", "formName1", "serialId1"
                ),
                foundation = null,
                documentId = "documentId1",
                intervalId = "intervalId1",
            ),
            userId = 1,
        ), id = Event.Id("1"), tenantId = 1, status = EventStatus.QUEUED, entityMetadata = EntityMetadata.now()
    )

    val triggerStepTypes = listOf(
        FlowStepTypeConfiguration(
            id = 1,
            type = "trigger",
            name = "Manual trigger from a form",
            description = "Trigger the flow manually from a button on a form in collection",
            tenantId = 1,
            primaryIdentifier = "manualTriggerFromForm",
            properties = FlowStepType.Properties(
                configuration = Configuration(
                    subscribeTo = mapOf(
                        EventKey.START_FLOW_MANUALLY_FROM_FORM to TriggerEventSubscription(
                            key = EventKey.START_FLOW_MANUALLY_FROM_FORM, condition = JsonObject(
                                mapOf(
                                    "AND" to JsonArray(
                                        listOf(
                                            JsonObject(
                                                mapOf(
                                                    "lhs" to JsonPrimitive("{{thisStep.buttonLabel}}"),
                                                    "operator" to JsonPrimitive("="),
                                                    "rhs" to JsonPrimitive("{{event.eventProperties.buttonLabel}}")
                                                )
                                            ), JsonObject(
                                                mapOf(
                                                    "lhs" to JsonPrimitive("{{event.eventProperties.form.formConfiguration.id}}"),
                                                    "operator" to JsonPrimitive("="),
                                                    "rhs" to JsonPrimitive("{{thisStep.formConfigurationId}}")
                                                )
                                            )
                                        )
                                    )
                                )
                            ), variableMappings = listOf(
                                VariableMapping(
                                    identifier = "{{thisStep.formVariableName}}",
                                    value = JsonPrimitive("{{event.eventProperties.form.id}}"),
                                    type = "form.{{thisStep.formConfigurationId}}",
                                )
                            )
                        )
                    )
                )
            )
        ), FlowStepTypeConfiguration(
            id = 2,
            type = "trigger",
            name = "Foundation created",
            description = "A trigger that executes when a foundation is created",
            tenantId = 1,
            primaryIdentifier = "foundationCreated",
            properties = FlowStepType.Properties(
                configuration = Configuration(
                    subscribeTo = mapOf(
                        EventKey.CREATE_COLLECTION_FOUNDATION to TriggerEventSubscription(
                            key = EventKey.CREATE_COLLECTION_FOUNDATION, condition = JsonObject(
                                mapOf(
                                    "lhs" to JsonPrimitive("{{event.eventProperties.foundation.foundationConfiguration.id}}"),
                                    "operator" to JsonPrimitive("="),
                                    "rhs" to JsonPrimitive("{{thisStep.foundationConfigurationId}}")
                                )
                            ), variableMappings = listOf(
                                VariableMapping(
                                    identifier = "{{thisStep.foundationVariableName}}",
                                    value = JsonPrimitive("{{event.eventProperties.foundation.id}}"),
                                    type = "foundation.{{thisStep.foundationConfigurationId}}",
                                )
                            )
                        )
                    )
                )
            ),
        )
    )

    val flowConfigurations = listOf(
        FlowConfiguration.ForJson(
            id = FlowConfiguration.Id("1"),
            name = FlowConfiguration.Name("flow1"),
            description = FlowConfiguration.Description("flow1"),
            metadata = EntityMetadata.now(),
            start = FlowConfiguration.Step.Id("A"),
            triggers = mapOf(
                FlowConfiguration.Step.Id("A") to FlowConfiguration.Trigger(
                    id = FlowConfiguration.Step.Id("A"),
                    name = "Set Variables A",
                    properties = FlowConfiguration.Step.Properties(
                        typePrimaryIdentifier = "manualTriggerFromForm", inputs = mapOf(
                            "buttonLabel" to JsonPrimitive("Submit"),
                            "formConfigurationId" to JsonPrimitive("1231"),
                            "formVariableName" to JsonPrimitive("formVariableName1")
                        )
                    )
                ), FlowConfiguration.Step.Id("B") to FlowConfiguration.Trigger(
                    id = FlowConfiguration.Step.Id("B"),
                    name = "Set Variables B",
                    properties = FlowConfiguration.Step.Properties(
                        typePrimaryIdentifier = "manualTriggerFromForm", inputs = mapOf(
                            "buttonLabel" to JsonPrimitive("Submit"),
                            "formConfigurationId" to JsonPrimitive("formConfigurationId1"),
                            "formVariableName" to JsonPrimitive("formVariableName1")
                        )
                    )
                )
            )
        ), FlowConfiguration.ForJson(
            id = FlowConfiguration.Id("2"),
            name = FlowConfiguration.Name("flow2"),
            description = FlowConfiguration.Description("flow2"),
            metadata = EntityMetadata.now(),
            start = FlowConfiguration.Step.Id("C"),
            triggers = mapOf(
                FlowConfiguration.Step.Id("C") to FlowConfiguration.Trigger(
                    id = FlowConfiguration.Step.Id("C"),
                    name = "Set Variables C",
                    properties = FlowConfiguration.Step.Properties(
                        typePrimaryIdentifier = "foundationCreated", inputs = mapOf(
                            "buttonLabel" to JsonPrimitive("Submit"), "formConfigurationId" to JsonPrimitive("1232")
                        )
                    )
                ),
            )
        )
    )

    val activeWorkspaceVersion = WorkspaceVersion.ForApi(
        id = WorkspaceVersion.Id(1), workspaceId = Workspace.Id(1), configuration = mock(Workspace.ForJson::class.java)
    )

    val triggerFlowEventListener = TriggerFlowEventListener(
        workspaceVersionService, proxyService, flowStepTypeConfigurationService, false, timeoutMins = 2
    )

    @Test
    fun `test getSubscribedTriggerFlowStepTypeConfigurations`() = runTest {
        `when`(flowStepTypeConfigurationService.getAllByQuery(mapOf("type" to listOf("trigger")))).thenReturn(
            triggerStepTypes
        )

        val subscribedTriggerFlowStepTypeConfigurations: List<FlowStepTypeConfiguration> =
            triggerFlowEventListener.getSubscribedTriggerFlowStepTypeConfigurations(event)

        assertNotNull(subscribedTriggerFlowStepTypeConfigurations)
        assertEquals(
            listOf(triggerStepTypes[0]), subscribedTriggerFlowStepTypeConfigurations
        )

    }

    @Test
    fun `test getFlowsUsingSubscribedTriggerFlowStepType`() = runTest {
        `when`(flowStepTypeConfigurationService.getAllByQuery(mapOf("type" to listOf("trigger")))).thenReturn(
            triggerStepTypes
        )

        val subscribedTriggerFlowStepTypeConfigurations: List<FlowStepTypeConfiguration> =
            triggerFlowEventListener.getSubscribedTriggerFlowStepTypeConfigurations(event)

        val flowsUsingASubscribedTriggerFlowStepType: List<FlowsUsingASubscribedTriggerFlowStepType> =
            triggerFlowEventListener.getFlowsUsingSubscribedTriggerFlowStepType(
                flowConfigurations, subscribedTriggerFlowStepTypeConfigurations
            )

        assertNotNull(flowsUsingASubscribedTriggerFlowStepType)
        assertEquals(
            listOf(triggerStepTypes[0]), subscribedTriggerFlowStepTypeConfigurations
        )

    }

    @Test
    fun `test isMatchingCondition is false`() = runTest {
        val triggerId = FlowConfiguration.Step.Id("A")
        val step = flowConfigurations.firstOrNull()?.triggers?.get(triggerId) as FlowConfiguration.Trigger

        val context = FlowContext(
            global = GlobalVariables(
                workspaceId = Workspace.Id(1),
                workspaceVersionId = WorkspaceVersion.Id(1),
                tenantId = 1,
                flowConfigurationId = FlowConfiguration.Id("1"),
                flowConfigurationName = FlowConfiguration.Name("flow")
            ),
            event = Event.ForApi(
                event.workspaceId,
                event.eventProperties,
                event.id,
                event.tenantId,
            ),
        )
        val contextWithLocal = FlowContextWithLocalStep(
            stepId = FlowExecution.Step.Id("A"),
            flowContext = context,
            thisStep = step.properties.inputs.mapValues { it.value as JsonPrimitive }.toMutableMap()
        )

        val trigger = step.toExecution()
        val contextToJsonObjectBuilder = ContextToJsonObjectBuilder(emptyList(), trigger.id.value)
        TriggerTemplatePopulator(contextWithLocal, contextToJsonObjectBuilder).populateTrigger(
            trigger, triggerStepTypes[0].properties?.configuration!!
        )

        val result: Boolean = triggerFlowEventListener.isMatchingCondition(trigger.condition!!.toExpression())

        assertEquals(
            false, result
        )
    }

    @Test
    fun `test isMatchingCondition is true`() = runTest {

        val triggerId = FlowConfiguration.Step.Id("B")
        val step = flowConfigurations.firstOrNull()?.triggers?.get(triggerId) as FlowConfiguration.Trigger

        val context = FlowContext(
            global = GlobalVariables(
                workspaceId = Workspace.Id(1),
                workspaceVersionId = WorkspaceVersion.Id(1),
                tenantId = 1,
                flowConfigurationId = FlowConfiguration.Id("1"),
                flowConfigurationName = FlowConfiguration.Name("flow")
            ),
            event = Event.ForApi(
                event.workspaceId,
                event.eventProperties,
                event.id,
                event.tenantId,
            ),
        )
        val contextWithLocal = FlowContextWithLocalStep(
            stepId = FlowExecution.Step.Id("A"),
            flowContext = context,
            thisStep = step.properties.inputs.mapValues { it.value as JsonPrimitive }.toMutableMap()
        )

        val trigger = step.toExecution()
        val contextToJsonObjectBuilder = ContextToJsonObjectBuilder(emptyList(), trigger.id.value)
        TriggerTemplatePopulator(contextWithLocal, contextToJsonObjectBuilder).populateTrigger(
            trigger, triggerStepTypes[0].properties?.configuration!!
        )

        val result: Boolean = triggerFlowEventListener.isMatchingCondition(trigger.condition!!.toExpression())

        assertEquals(
            true, result
        )

    }

    @Test
    fun `test checkTriggerMatch should call triggerFlow when isMatchingCondition is true`() = runTest {
        triggerFlowEventListener.checkTriggerMatch(
            activeWorkspaceVersion, event, flowConfigurations[0], triggerStepTypes[0]

        )

        coVerify(exactly = 1) {
            proxyService.call(any())
        }
    }
}

private fun mockProxyService(): ProxyService {
    val httpResponse = ProxyEndpointResponse(
        response = """
        {
            "success": true,
            "data": {
                "id": "1",
                "workspaceId": 1,
                "status": "COMPLETED",
                "result": "SUCCESS",
                "documentId": "documentId1",
                "configurationId": "1",
                "flowConfigurationName": "flow1"
            }
        }
    """.trimIndent(), status = ProxyEndpointResponseStatus.SUCCESS, error = null
    )
    val proxyService = mockk<ProxyService>()
    coEvery { proxyService.call(any()) } returns httpResponse
    mockkObject(ProxyService)
    coEvery { ProxyService.buildInternalTenantUrl(any()) } returns "http://localhost:8080//ai/api"
    return proxyService
}